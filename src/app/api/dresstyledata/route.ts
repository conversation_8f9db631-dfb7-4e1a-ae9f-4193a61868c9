import { mkdir, writeFile } from "fs/promises";
import { NextRequest, NextResponse } from "next/server";
import path from "path";
import { PrismaClient } from "../../../../generated/prisma";

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    
    const [title,currentImg,previousImg,colspan,colstart] = ["title","currentImg","previousImg","colstart","colspan"].map(items=>formData.getAll(items));

    return NextResponse.json(previousImg)

    // get the filename
    // const fileName = imgpath.map(items=> items instanceof File? items.name : items);
    // // renameFile
    // const reformation = fileName.map((items)=>{
    //     const separate = items?.split(".");
    //     const imgName = separate?.[0];
    //     const renameImg = (imgName?imgName : "") + Date.now();
    //     const addFormat = renameImg + "." + separate?.[1];

    //     return addFormat;
    // });

    // const fileFormation = imgpath.map((items)=>{
    //     if(items instanceof File){
    //         const fileName = items.name;
    //         const separate = fileName?.split(".");
    //         const imgName = separate?.[0];
    //         const renameImg=(imgName?imgName:"") + Date.now();
    //         const addFormat= renameImg + "." + separate?.[1];

    //          return addFormat;
    //     }else{
    //         return items;
    //     }
    // });

    // bufferfile
    // const buffer = await Promise.all(
    //     imgpath.map(async (items)=>{
    //         return items instanceof File ? Buffer.from(await items.arrayBuffer()) : items;
    //     })
    // );
    // place file to the public folder
    // const uploadDir = path.join(process.cwd(),"public/assets");

    // const arr = title.map((items,index)=>({
    //     title: typeof items === 'string' ? items : null,
    //     imgpath: fileFormation[index],
    //     colstart: typeof colStart[index] === 'string' ? colStart[index] : null,
    //     colspan: typeof colSpan[index] === 'string' ? colSpan[index] : null
    // }));

    // try{
    //     await mkdir(uploadDir,{recursive:true});

    //     await Promise.all(fileFormation.map(async(items,index)=>{
    //         await writeFile(path.join(process.cwd(),"public/assets/" + items),buffer[index])
    //     }));

    //     await prisma.dresstyle.createMany({
    //         data:arr
    //     });

    //     return NextResponse.json({message:"added"});
    // }catch(error){
    //     return NextResponse.json(error)
    // }
}

// ===========first-step===============
// image store with unique name
// for this we have to code something
// after store place it to array of object
// save it to database

// ===========second-step===============
// retrieve all data 
// before upload any data with image first check whether the image exist on public folder or not
// if mismatch-> remove corresponding the previous one & listed new one.
// if not-mismatch-> continue to the next step

// ===========thinking-step===============
// from client side we will get info of two props
// one is previous value(retrieve value from server) & second is current value(client side)
// before listed current , matched with previous one
// if match then nothing to do, if not then remove the file base on previous data
